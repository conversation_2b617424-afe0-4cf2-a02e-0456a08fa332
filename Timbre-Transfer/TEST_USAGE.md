# Ray Serve Timbre Transfer API - Test Scripts Usage Guide

This guide explains how to use the test scripts to evaluate your Ray Serve API's performance and concurrent request handling capabilities.

## 📁 Test Files Available

- **`quick_test.py`** - Simple single request test
- **`test_ray_serve_api.py`** - Advanced concurrent load testing
- **`batch_test.py`** - Comprehensive test suite with multiple scenarios
- **Audio files used:**
  - `Deepak paudel.mp3` (source audio)
  - `canadian_cheerful.mp3` (reference audio)

## 🚀 Quick Start

### 1. Health Check Only
```bash
# Check if API is running and healthy
python3 quick_test.py --health-only
```

### 2. Single Request Test
```bash
# Test with one request (fastest way to verify API works)
python3 quick_test.py
```

### 3. Test API Statistics
```bash
# Get current API stats and GPU usage
python3 quick_test.py --stats-only
```

## ⚡ Load Testing

### Test 9 Concurrent Requests (Max Capacity)
```bash
# Test maximum expected capacity (3 GPUs × 3 requests each)
python3 test_ray_serve_api.py --requests 9 --concurrent 9
```

### Test Different Load Levels
```bash
# Light load (3 concurrent)
python3 test_ray_serve_api.py --requests 6 --concurrent 3

# Medium load (6 concurrent)
python3 test_ray_serve_api.py --requests 9 --concurrent 6

# Heavy load (12 concurrent - overload test)
python3 test_ray_serve_api.py --requests 12 --concurrent 12
```

### Sequential Testing (No Concurrency)
```bash
# Test 6 requests one after another
python3 test_ray_serve_api.py --requests 6 --concurrent 1
```

## 🧪 Comprehensive Testing

### Quick Batch Test (3 scenarios)
```bash
# Run essential tests only
python3 batch_test.py --quick
```

### Full Batch Test (7 scenarios)
```bash
# Run complete test suite
python3 batch_test.py
```

### Custom Report Name
```bash
# Save report with custom filename
python3 batch_test.py --report my_performance_test.md
```

## 📊 Understanding Results

### Key Metrics to Watch

1. **Success Rate**: Should be 100% for loads ≤ 9 concurrent requests
2. **Latency**: Expected 8-12 seconds per request
3. **Throughput**: Should handle 9 concurrent requests efficiently
4. **Server vs Total Latency**: Server latency = actual processing time

### Expected Performance

| Concurrent Requests | Expected Result | Notes |
|-------------------|-----------------|-------|
| 1-3 | ✅ Fast & Reliable | Optimal performance |
| 4-6 | ✅ Good Performance | Still within capacity |
| 7-9 | ✅ Max Capacity | All 3 GPUs fully utilized |
| 10+ | ⚠️ May Queue/Fail | Exceeds GPU capacity |

## 🔍 Troubleshooting

### API Not Responding
```bash
# Check if server is running
curl http://localhost:8011/health

# Check Ray Serve status
python3 quick_test.py --health-only
```

### High Latency Issues
```bash
# Check GPU memory usage
python3 quick_test.py --stats-only

# Test with single request first
python3 quick_test.py
```

### Failed Requests
- **Timeout errors**: Increase timeout or reduce concurrent requests
- **Memory errors**: Check GPU memory usage in stats
- **Connection errors**: Verify API server is running

## 📈 Performance Optimization Tips

### For Best Results:
1. **Start with health check** to ensure API is ready
2. **Test single request first** to verify basic functionality
3. **Gradually increase load** (1 → 3 → 6 → 9 requests)
4. **Monitor GPU memory** using stats endpoint
5. **Wait between test runs** to let GPU memory clear

### Interpreting Results:
- **Success Rate < 100%**: API is overloaded
- **High Latency Variance**: Inconsistent performance
- **Memory Errors**: Need to reduce concurrent requests
- **Timeout Errors**: Requests taking too long

## 🎯 Test Scenarios Explained

### Batch Test Scenarios:

1. **Single Request**: Baseline performance test
2. **Low Load (3 concurrent)**: Light usage simulation
3. **Medium Load (6 concurrent)**: Moderate usage
4. **High Load (9 concurrent)**: Maximum capacity test
5. **Overload Test (12 concurrent)**: Stress test beyond capacity
6. **Sequential Test**: No concurrency, measures raw throughput
7. **Burst Test (15 concurrent)**: Extreme stress test

## 📄 Report Generation

All batch tests generate detailed markdown reports with:
- Success/failure rates
- Latency statistics
- Throughput measurements
- Error analysis
- Performance recommendations

Reports are saved as `ray_serve_test_report_YYYYMMDD_HHMMSS.md`

## 🔧 Advanced Usage

### Custom API URL
```bash
# Test different server
python3 test_ray_serve_api.py --url http://your-server:8011
```

### Custom Audio Files
```bash
# Use different audio files
python3 quick_test.py --source your_source.mp3 --reference your_ref.mp3
```

### Debug Mode
```bash
# Get detailed output
python3 test_ray_serve_api.py --requests 3 --concurrent 3 -v
```

## 🎵 Expected Output Example

```
🎵 Timbre Transfer Load Tester
📁 Source audio: Deepak paudel.mp3
📁 Reference audio: canadian_cheerful.mp3
🔧 Noise cancellation: False
🔧 Audio normalization: False
🎯 Flow matching steps: 32

🚀 Starting concurrent test:
📊 Total requests: 9
⚡ Max concurrent: 9
⏱️ Total test time: 12.45 seconds

📊 Test Results Summary:
==================================================
Total Requests: 9
Successful: 9
Failed: 0
Success Rate: 100.0%

⏱️ Latency Statistics (seconds):
  Min: 8.23s
  Max: 11.87s
  Mean: 9.45s
  Median: 9.12s
  Std Dev: 1.23s

🚀 Throughput:
  Requests/second: 0.72
  Avg Response Size: 2.34 MB
```

This comprehensive test suite will help you understand exactly how your Ray Serve API handles concurrent requests and identify the optimal load for your 3×16GB GPU setup! 🎯
