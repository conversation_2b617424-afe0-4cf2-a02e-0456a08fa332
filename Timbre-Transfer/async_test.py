#!/usr/bin/env python3
"""
Simple Async Test for Ray Serve Timbre Transfer API
Tests concurrent request handling with async/await
"""

import asyncio
import aiohttp
import time
import os
import argparse
from typing import List

async def test_single_request(session: aiohttp.ClientSession, request_id: int, base_url: str) -> dict:
    """Test a single timbre transfer request"""
    print(f"🚀 Starting request {request_id}")
    start_time = time.time()
    
    try:
        # Prepare form data
        data = aiohttp.FormData()
        
        # Read audio files into memory first
        with open("Deepak paudel.mp3", 'rb') as f:
            source_data = f.read()

        with open("canadian_cheerful.mp3", 'rb') as f:
            ref_data = f.read()

        # Add audio files to form data
        data.add_field('source_audio', source_data, filename="Deepak paudel.mp3", content_type='audio/mpeg')
        data.add_field('reference_audio', ref_data, filename="canadian_cheerful.mp3", content_type='audio/mpeg')
        
        # Add parameters (default values)
        data.add_field('noise_cancellation', 'false')
        data.add_field('normalize_audio_flag', 'false')
        data.add_field('flow_matching_steps', '20')  # Optimized: reduced from 32
        
        # Send request
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes
        async with session.post(f"{base_url}/timbre-transfer/", data=data, timeout=timeout) as response:
            end_time = time.time()
            latency = end_time - start_time
            
            if response.status == 200:
                content = await response.read()
                server_latency = response.headers.get('X-Inference-Latency', 'N/A')
                
                # Save output file
                output_file = f"output_request_{request_id}_{int(time.time())}.wav"
                with open(output_file, 'wb') as f:
                    f.write(content)
                
                print(f"✅ Request {request_id} completed in {latency:.2f}s (server: {server_latency}s)")
                return {
                    "request_id": request_id,
                    "success": True,
                    "latency": latency,
                    "server_latency": server_latency,
                    "response_size_mb": len(content) / (1024 * 1024),
                    "output_file": output_file
                }
            else:
                error_text = await response.text()
                print(f"❌ Request {request_id} failed: HTTP {response.status}")
                return {
                    "request_id": request_id,
                    "success": False,
                    "latency": latency,
                    "error": f"HTTP {response.status}: {error_text}"
                }
                
    except asyncio.TimeoutError:
        print(f"⏰ Request {request_id} timed out")
        return {
            "request_id": request_id,
            "success": False,
            "latency": time.time() - start_time,
            "error": "Timeout"
        }
    except Exception as e:
        print(f"💥 Request {request_id} error: {e}")
        return {
            "request_id": request_id,
            "success": False,
            "latency": time.time() - start_time,
            "error": str(e)
        }

async def test_concurrent_requests(num_requests: int, base_url: str) -> List[dict]:
    """Test multiple concurrent requests"""
    print(f"\n🎵 Testing {num_requests} concurrent timbre transfer requests")
    print(f"🌐 API URL: {base_url}")
    print(f"📁 Source: Deepak paudel.mp3")
    print(f"📁 Reference: canadian_cheerful.mp3")
    print(f"🔧 Using optimized settings (no noise cancellation, no normalization, 20 flow steps)")
    
    # Check if audio files exist
    if not os.path.exists("Deepak paudel.mp3"):
        print("❌ Source audio file 'Deepak paudel.mp3' not found!")
        return []
    
    if not os.path.exists("canadian_cheerful.mp3"):
        print("❌ Reference audio file 'canadian_cheerful.mp3' not found!")
        return []
    
    print(f"\n🚀 Starting {num_requests} concurrent requests...")
    start_time = time.time()
    
    # Create session with connection limits
    connector = aiohttp.TCPConnector(limit=num_requests + 5)
    async with aiohttp.ClientSession(connector=connector) as session:
        # Create tasks for all requests
        tasks = [
            test_single_request(session, i, base_url) 
            for i in range(1, num_requests + 1)
        ]
        
        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "request_id": i + 1,
                    "success": False,
                    "latency": 0,
                    "error": str(result)
                })
            else:
                final_results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️ All requests completed in {total_time:.2f} seconds")
    
    return final_results

def analyze_results(results: List[dict]):
    """Analyze and print test results"""
    if not results:
        print("❌ No results to analyze")
        return
    
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    print(f"\n📊 Results Summary:")
    print(f"=" * 50)
    print(f"Total Requests: {len(results)}")
    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")
    print(f"Success Rate: {len(successful)/len(results)*100:.1f}%")
    
    if successful:
        latencies = [r['latency'] for r in successful]
        server_latencies = [float(r['server_latency']) for r in successful if r['server_latency'] != 'N/A']
        response_sizes = [r.get('response_size_mb', 0) for r in successful]
        
        print(f"\n⏱️ Latency Statistics:")
        print(f"  Min: {min(latencies):.2f}s")
        print(f"  Max: {max(latencies):.2f}s")
        print(f"  Average: {sum(latencies)/len(latencies):.2f}s")
        
        if server_latencies:
            print(f"\n🖥️ Server Latency Statistics:")
            print(f"  Min: {min(server_latencies):.2f}s")
            print(f"  Max: {max(server_latencies):.2f}s")
            print(f"  Average: {sum(server_latencies)/len(server_latencies):.2f}s")
        
        if response_sizes:
            avg_size = sum(response_sizes) / len(response_sizes)
            print(f"\n📦 Response Size: {avg_size:.2f} MB average")
        
        print(f"\n🚀 Throughput: {len(successful)/max(latencies):.2f} requests/second")
        
        print(f"\n💾 Output Files:")
        for r in successful:
            if 'output_file' in r:
                print(f"  Request {r['request_id']}: {r['output_file']}")
    
    if failed:
        print(f"\n❌ Failed Requests:")
        for r in failed:
            print(f"  Request {r['request_id']}: {r.get('error', 'Unknown error')}")

async def main():
    parser = argparse.ArgumentParser(description="Async test for Ray Serve Timbre Transfer API")
    parser.add_argument("--url", default="http://localhost:8011", help="API base URL")
    parser.add_argument("--requests", type=int, default=3, help="Number of concurrent requests")
    
    args = parser.parse_args()
    
    print(f"🎵 Async Timbre Transfer API Test")
    print(f"🌐 API URL: {args.url}")
    print(f"📊 Concurrent Requests: {args.requests}")
    
    # Run the test
    results = await test_concurrent_requests(args.requests, args.url)
    
    # Analyze results
    analyze_results(results)
    
    return 0 if results and all(r.get('success', False) for r in results) else 1

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
