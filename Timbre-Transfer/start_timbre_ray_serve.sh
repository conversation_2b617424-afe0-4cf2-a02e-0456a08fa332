#!/bin/bash

# Ray Serve Timbre Transfer API Startup Script
# This script starts the Ray Serve timbre transfer API with GPU acceleration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8010"
DEFAULT_GPU_IDS="0,2,3"  # Use GPUs 0, 2, 3 for timbre transfer (skip GPU 1)

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if GPU is available
check_gpu() {
    print_info "Checking GPU availability..."
    
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits
        print_success "GPU check completed"
        return 0
    else
        print_warning "nvidia-smi not found - GPU may not be available"
        return 1
    fi
}

# Function to activate virtual environment
activate_venv() {
    if [ -f ".venv/bin/activate" ]; then
        print_info "Activating virtual environment..."
        source .venv/bin/activate
        print_success "Virtual environment activated"
    else
        print_warning "Virtual environment not found at .venv/bin/activate"
    fi
}

# Function to check Python dependencies
check_dependencies() {
    print_info "Checking Python dependencies..."

    # Activate virtual environment first
    activate_venv

    python3 -c "
import torch
import ray
from ray import serve
print(f'PyTorch version: {torch.__version__}')
print(f'Ray version: {ray.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU count: {torch.cuda.device_count()}')
" || {
        print_error "Missing required dependencies. Please install them first."
        exit 1
    }

    print_success "Dependencies check passed"
}

# Function to start the server
start_server() {
    local host=${1:-$DEFAULT_HOST}
    local port=${2:-$DEFAULT_PORT}
    local gpu_ids=${3:-$DEFAULT_GPU_IDS}

    print_info "Starting Ray Serve Timbre Transfer API..."
    print_info "Host: $host"
    print_info "Port: $port"
    print_info "GPU IDs: $gpu_ids (GPU 0 reserved for other tasks)"
    print_info "Expected concurrent capacity: 18 requests (6 replicas × 3 requests each)"
    print_info "GPU memory: 8GB per replica, 2 replicas per GPU (48GB total optimized)"
    print_info "Optimizations: Mixed Precision (FP16) + Flow Steps (20) + GPU Memory Sharing"

    # Activate virtual environment
    activate_venv

    # Set CUDA_VISIBLE_DEVICES to use specific GPUs
    export CUDA_VISIBLE_DEVICES=$gpu_ids

    # Start the server with 3 GPUs
    python3 start_timbre_server.py \
        --host "$host" \
        --port "$port" \
        --num-gpus 3
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -c, --check-gpu         Check GPU availability and exit"
    echo "  -d, --check-deps        Check dependencies and exit"
    echo "  --host HOST             Host to bind to (default: $DEFAULT_HOST)"
    echo "  --port PORT             Port to bind to (default: $DEFAULT_PORT)"
    echo "  --gpu-ids GPU_IDS       GPU IDs to use (default: $DEFAULT_GPU_IDS)"
    echo "  --debug                 Enable debug mode"
    echo ""
    echo "Examples:"
    echo "  $0                      # Start with default settings"
    echo "  $0 --check-gpu          # Check GPU availability"
    echo "  $0 --host 0.0.0.0 --port 8010 --gpu-ids 0,2,3"
    echo "  $0 --debug              # Start with debug logging"
}

# Parse command line arguments
HOST=$DEFAULT_HOST
PORT=$DEFAULT_PORT
GPU_IDS=$DEFAULT_GPU_IDS
DEBUG=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--check-gpu)
            check_gpu
            exit $?
            ;;
        -d|--check-deps)
            check_dependencies
            exit 0
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --gpu-ids)
            GPU_IDS="$2"
            shift 2
            ;;
        --debug)
            DEBUG=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "🎵 Ray Serve Timbre Transfer API Startup"
    print_info "========================================"
    
    # Check dependencies
    check_dependencies
    
    # Check GPU
    if ! check_gpu; then
        print_warning "GPU check failed, but continuing anyway..."
    fi
    
    # Add debug flag if requested
    if [ "$DEBUG" = true ]; then
        print_info "Debug mode enabled"
        activate_venv
        export PYTHONPATH="${PYTHONPATH}:."
        python3 start_timbre_server.py \
            --host "$HOST" \
            --port "$PORT" \
            --num-gpus 1 \
            --debug
    else
        # Start the server
        start_server "$HOST" "$PORT" "$GPU_IDS"
    fi
}

# Trap Ctrl+C and cleanup
cleanup() {
    print_info "Shutting down server..."
    # Kill any remaining Python processes (be careful with this)
    # pkill -f "start_timbre_server.py" || true
    print_success "Cleanup completed"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Run main function
main "$@"
