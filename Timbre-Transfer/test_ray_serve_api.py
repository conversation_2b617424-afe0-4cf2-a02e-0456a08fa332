#!/usr/bin/env python3
"""
Ray Serve Timbre Transfer API Load Test Script

This script tests the concurrent request handling capabilities of the Ray Serve API
with multiple simultaneous requests to measure throughput and performance.
"""

import asyncio
import aiohttp
import time
import os
import json
import statistics
from typing import List, Dict, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import argparse

@dataclass
class TestResult:
    """Test result data structure"""
    request_id: int
    success: bool
    latency: float
    response_size: int
    error_message: str = ""
    server_latency: float = 0.0

class TimbreTransferLoadTester:
    """Load tester for Ray Serve Timbre Transfer API"""
    
    def __init__(self, base_url: str = "http://localhost:8011"):
        self.base_url = base_url
        self.source_audio = "Deepak paudel.mp3"
        self.reference_audio = "canadian_cheerful.mp3"
        
        # Test configuration
        self.noise_cancellation = False  # Default value
        self.normalize_audio = False     # Default value
        self.flow_matching_steps = 32    # Default value
        
        # Verify audio files exist
        if not os.path.exists(self.source_audio):
            raise FileNotFoundError(f"Source audio file not found: {self.source_audio}")
        if not os.path.exists(self.reference_audio):
            raise FileNotFoundError(f"Reference audio file not found: {self.reference_audio}")
        
        print(f"🎵 Timbre Transfer Load Tester")
        print(f"📁 Source audio: {self.source_audio}")
        print(f"📁 Reference audio: {self.reference_audio}")
        print(f"🔧 Noise cancellation: {self.noise_cancellation}")
        print(f"🔧 Audio normalization: {self.normalize_audio}")
        print(f"🎯 Flow matching steps: {self.flow_matching_steps}")
    
    async def check_api_health(self) -> bool:
        """Check if the API is healthy and ready"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print(f"✅ API Health Check: {health_data}")
                        return health_data.get("status") == "healthy"
                    else:
                        print(f"❌ Health check failed: HTTP {response.status}")
                        return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    async def get_api_stats(self) -> Dict[str, Any]:
        """Get API statistics"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/stats") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def single_request(self, request_id: int, session: aiohttp.ClientSession) -> TestResult:
        """Send a single timbre transfer request"""
        start_time = time.time()
        
        try:
            # Prepare form data
            data = aiohttp.FormData()
            
            # Add audio files
            with open(self.source_audio, 'rb') as f:
                data.add_field('source_audio', f, filename=self.source_audio, content_type='audio/mpeg')
            
            with open(self.reference_audio, 'rb') as f:
                data.add_field('reference_audio', f, filename=self.reference_audio, content_type='audio/mpeg')
            
            # Add parameters
            data.add_field('noise_cancellation', str(self.noise_cancellation).lower())
            data.add_field('normalize_audio_flag', str(self.normalize_audio).lower())
            data.add_field('flow_matching_steps', str(self.flow_matching_steps))
            
            # Send request with timeout
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
            async with session.post(f"{self.base_url}/timbre-transfer/", data=data, timeout=timeout) as response:
                end_time = time.time()
                latency = end_time - start_time
                
                if response.status == 200:
                    # Get response content
                    content = await response.read()
                    response_size = len(content)
                    
                    # Get server-side latency from headers
                    server_latency = float(response.headers.get('X-Inference-Latency', 0))
                    
                    return TestResult(
                        request_id=request_id,
                        success=True,
                        latency=latency,
                        response_size=response_size,
                        server_latency=server_latency
                    )
                else:
                    error_text = await response.text()
                    return TestResult(
                        request_id=request_id,
                        success=False,
                        latency=latency,
                        response_size=0,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except asyncio.TimeoutError:
            return TestResult(
                request_id=request_id,
                success=False,
                latency=time.time() - start_time,
                response_size=0,
                error_message="Request timeout"
            )
        except Exception as e:
            return TestResult(
                request_id=request_id,
                success=False,
                latency=time.time() - start_time,
                response_size=0,
                error_message=str(e)
            )
    
    async def concurrent_test(self, num_requests: int, max_concurrent: int = None) -> List[TestResult]:
        """Run concurrent requests test"""
        if max_concurrent is None:
            max_concurrent = num_requests
        
        print(f"\n🚀 Starting concurrent test:")
        print(f"📊 Total requests: {num_requests}")
        print(f"⚡ Max concurrent: {max_concurrent}")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_request(request_id: int, session: aiohttp.ClientSession) -> TestResult:
            async with semaphore:
                return await self.single_request(request_id, session)
        
        # Create session with connection limits
        connector = aiohttp.TCPConnector(limit=max_concurrent + 5)
        async with aiohttp.ClientSession(connector=connector) as session:
            # Create tasks for all requests
            tasks = [
                limited_request(i, session) 
                for i in range(1, num_requests + 1)
            ]
            
            # Execute all requests concurrently
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Handle any exceptions
            test_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    test_results.append(TestResult(
                        request_id=i + 1,
                        success=False,
                        latency=0,
                        response_size=0,
                        error_message=str(result)
                    ))
                else:
                    test_results.append(result)
            
            total_time = end_time - start_time
            print(f"⏱️ Total test time: {total_time:.2f} seconds")
            
            return test_results
    
    def analyze_results(self, results: List[TestResult]) -> Dict[str, Any]:
        """Analyze test results and generate statistics"""
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        if not successful_results:
            return {
                "total_requests": len(results),
                "successful_requests": 0,
                "failed_requests": len(failed_results),
                "success_rate": 0.0,
                "error": "No successful requests"
            }
        
        # Calculate statistics
        latencies = [r.latency for r in successful_results]
        server_latencies = [r.server_latency for r in successful_results if r.server_latency > 0]
        response_sizes = [r.response_size for r in successful_results]
        
        analysis = {
            "total_requests": len(results),
            "successful_requests": len(successful_results),
            "failed_requests": len(failed_results),
            "success_rate": len(successful_results) / len(results) * 100,
            
            "latency_stats": {
                "min": min(latencies),
                "max": max(latencies),
                "mean": statistics.mean(latencies),
                "median": statistics.median(latencies),
                "std_dev": statistics.stdev(latencies) if len(latencies) > 1 else 0
            },
            
            "throughput": {
                "requests_per_second": len(successful_results) / max(latencies) if latencies else 0,
                "avg_response_size_mb": statistics.mean(response_sizes) / (1024 * 1024) if response_sizes else 0
            }
        }
        
        if server_latencies:
            analysis["server_latency_stats"] = {
                "min": min(server_latencies),
                "max": max(server_latencies),
                "mean": statistics.mean(server_latencies),
                "median": statistics.median(server_latencies)
            }
        
        # Error analysis
        if failed_results:
            error_counts = {}
            for result in failed_results:
                error = result.error_message
                error_counts[error] = error_counts.get(error, 0) + 1
            analysis["errors"] = error_counts
        
        return analysis
    
    def print_results(self, analysis: Dict[str, Any]):
        """Print formatted test results"""
        print(f"\n📊 Test Results Summary:")
        print(f"=" * 50)
        print(f"Total Requests: {analysis['total_requests']}")
        print(f"Successful: {analysis['successful_requests']}")
        print(f"Failed: {analysis['failed_requests']}")
        print(f"Success Rate: {analysis['success_rate']:.1f}%")
        
        if 'latency_stats' in analysis:
            lat = analysis['latency_stats']
            print(f"\n⏱️ Latency Statistics (seconds):")
            print(f"  Min: {lat['min']:.2f}s")
            print(f"  Max: {lat['max']:.2f}s")
            print(f"  Mean: {lat['mean']:.2f}s")
            print(f"  Median: {lat['median']:.2f}s")
            print(f"  Std Dev: {lat['std_dev']:.2f}s")
        
        if 'server_latency_stats' in analysis:
            slat = analysis['server_latency_stats']
            print(f"\n🖥️ Server Latency Statistics (seconds):")
            print(f"  Min: {slat['min']:.2f}s")
            print(f"  Max: {slat['max']:.2f}s")
            print(f"  Mean: {slat['mean']:.2f}s")
            print(f"  Median: {slat['median']:.2f}s")
        
        if 'throughput' in analysis:
            thr = analysis['throughput']
            print(f"\n🚀 Throughput:")
            print(f"  Requests/second: {thr['requests_per_second']:.2f}")
            print(f"  Avg Response Size: {thr['avg_response_size_mb']:.2f} MB")
        
        if 'errors' in analysis:
            print(f"\n❌ Errors:")
            for error, count in analysis['errors'].items():
                print(f"  {error}: {count} times")

async def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description="Ray Serve Timbre Transfer API Load Tester")
    parser.add_argument("--url", default="http://localhost:8011", help="API base URL")
    parser.add_argument("--requests", type=int, default=9, help="Number of requests to send")
    parser.add_argument("--concurrent", type=int, default=9, help="Max concurrent requests")
    parser.add_argument("--health-only", action="store_true", help="Only check API health")
    parser.add_argument("--stats-only", action="store_true", help="Only get API stats")
    
    args = parser.parse_args()
    
    # Create tester
    tester = TimbreTransferLoadTester(args.url)
    
    # Health check only
    if args.health_only:
        healthy = await tester.check_api_health()
        return 0 if healthy else 1
    
    # Stats only
    if args.stats_only:
        stats = await tester.get_api_stats()
        print(json.dumps(stats, indent=2))
        return 0
    
    # Check API health first
    print("🔍 Checking API health...")
    if not await tester.check_api_health():
        print("❌ API is not healthy. Exiting.")
        return 1
    
    # Get initial stats
    print("\n📊 Getting initial API stats...")
    initial_stats = await tester.get_api_stats()
    print(json.dumps(initial_stats, indent=2))
    
    # Run load test
    results = await tester.concurrent_test(args.requests, args.concurrent)
    
    # Analyze and print results
    analysis = tester.analyze_results(results)
    tester.print_results(analysis)
    
    # Get final stats
    print("\n📊 Getting final API stats...")
    final_stats = await tester.get_api_stats()
    print(json.dumps(final_stats, indent=2))
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
