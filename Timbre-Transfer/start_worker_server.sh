#!/bin/bash

# Ray Worker Server Startup Script
# This script properly starts a worker server that contributes resources to the Ray cluster

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_worker() {
    echo -e "${PURPLE}[WORKER]${NC} $1"
}

# Default configuration
DEFAULT_HEAD_NODE_IP=""
DEFAULT_HEAD_NODE_PORT="10001"
DEFAULT_API_PORT="8009"
DEFAULT_NUM_GPUS="auto"

# Parse command line arguments
HEAD_NODE_IP=""
HEAD_NODE_PORT="$DEFAULT_HEAD_NODE_PORT"
API_PORT="$DEFAULT_API_PORT"
NUM_GPUS="$DEFAULT_NUM_GPUS"

show_usage() {
    echo "Usage: $0 --head-ip <HEAD_NODE_IP> [OPTIONS]"
    echo ""
    echo "Required:"
    echo "  --head-ip IP        IP address of the Ray head node"
    echo ""
    echo "Optional:"
    echo "  --head-port PORT    Ray head node port (default: $DEFAULT_HEAD_NODE_PORT)"
    echo "  --api-port PORT     API server port (default: $DEFAULT_API_PORT)"
    echo "  --num-gpus N        Number of GPUs to contribute (default: auto-detect)"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --head-ip *************"
    echo "  $0 --head-ip ************* --num-gpus 2 --api-port 8010"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --head-ip)
            HEAD_NODE_IP="$2"
            shift 2
            ;;
        --head-port)
            HEAD_NODE_PORT="$2"
            shift 2
            ;;
        --api-port)
            API_PORT="$2"
            shift 2
            ;;
        --num-gpus)
            NUM_GPUS="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [ -z "$HEAD_NODE_IP" ]; then
    print_error "Head node IP is required"
    show_usage
    exit 1
fi

# Auto-detect GPUs if needed
if [ "$NUM_GPUS" = "auto" ]; then
    if command -v nvidia-smi &> /dev/null; then
        NUM_GPUS=$(nvidia-smi --list-gpus | wc -l)
        print_info "Auto-detected GPUs: $NUM_GPUS"
    else
        NUM_GPUS=0
        print_warning "nvidia-smi not found, setting GPUs to 0"
    fi
fi

print_worker "🔗 Ray Worker Server Startup"
print_worker "============================="
print_info "Head node: $HEAD_NODE_IP:$HEAD_NODE_PORT"
print_info "API port: $API_PORT"
print_info "GPUs: $NUM_GPUS"

# Check if Ray is already running
if pgrep -f "ray start" > /dev/null; then
    print_error "Ray worker is already running. Stop it first with: ray stop"
    exit 1
fi

# Test connection to head node
print_info "Testing connection to head node..."
if ! nc -z "$HEAD_NODE_IP" "$HEAD_NODE_PORT" 2>/dev/null; then
    print_error "Cannot connect to head node at $HEAD_NODE_IP:$HEAD_NODE_PORT"
    print_info "Make sure the head node is running and accessible"
    exit 1
fi
print_success "✅ Head node is reachable"

# Activate virtual environment if it exists
if [ -f ".venv/bin/activate" ]; then
    print_info "Activating virtual environment..."
    source .venv/bin/activate
fi

# Start the worker server with proper Ray worker node configuration
print_worker "🚀 Starting worker server that contributes resources to cluster..."

RAY_ADDRESS="$HEAD_NODE_IP:$HEAD_NODE_PORT"

python start_timbre_server.py \
    --worker-node \
    --ray-address "$RAY_ADDRESS" \
    --port "$API_PORT" \
    --num-gpus "$NUM_GPUS" \
    --host "0.0.0.0"

print_success "✅ Worker server startup completed"
