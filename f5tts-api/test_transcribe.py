#!/usr/bin/env python3
"""
Test script to compare transcription results between /synthesize and /transcribe endpoints
"""
import sys
import os
sys.path.append('src')

from f5_tts.infer.utils_infer import preprocess_ref_audio_text, transcribe

def test_transcription_difference():
    """Test the difference between synthesis and transcribe processing"""
    
    audio_file = "download.mp3"
    
    if not os.path.exists(audio_file):
        print(f"Audio file {audio_file} not found!")
        return
    
    print("=" * 60)
    print("TESTING TRANSCRIPTION DIFFERENCES")
    print("=" * 60)
    
    # Test 1: Simulate what happens in /synthesize endpoint
    print("\n1. SYNTHESIZE ENDPOINT PROCESSING:")
    print("-" * 40)
    try:
        processed_audio_path, processed_ref_text = preprocess_ref_audio_text(
            audio_file, "", show_info=print
        )
        print(f"\nSYNTHESIZE RESULT:")
        print(f"Processed text: '{processed_ref_text}'")
        print(f"Text length: {len(processed_ref_text)}")
        print(f"Ends with '. ': {processed_ref_text.endswith('. ')}")
        print(f"Ends with '。': {processed_ref_text.endswith('。')}")
    except Exception as e:
        print(f"Error in synthesize processing: {e}")
        return
    
    # Test 2: Simulate what happens in /transcribe endpoint (original version)
    print("\n\n2. TRANSCRIBE ENDPOINT PROCESSING (ORIGINAL):")
    print("-" * 50)
    try:
        # Get processed audio path
        processed_audio_path_2, _ = preprocess_ref_audio_text(audio_file, "", show_info=print)
        
        # Transcribe directly
        transcribed_text_original = transcribe(processed_audio_path_2, language=None)
        print(f"\nTRANSCRIBE ORIGINAL RESULT:")
        print(f"Transcribed text: '{transcribed_text_original}'")
        print(f"Text length: {len(transcribed_text_original)}")
        print(f"Ends with '. ': {transcribed_text_original.endswith('. ')}")
        print(f"Ends with '。': {transcribed_text_original.endswith('。')}")
    except Exception as e:
        print(f"Error in original transcribe processing: {e}")
        return
    
    # Test 3: Simulate what happens in /transcribe endpoint (fixed version)
    print("\n\n3. TRANSCRIBE ENDPOINT PROCESSING (FIXED):")
    print("-" * 48)
    try:
        # Get processed audio path
        processed_audio_path_3, _ = preprocess_ref_audio_text(audio_file, "", show_info=print)
        
        # Transcribe directly
        transcribed_text_fixed = transcribe(processed_audio_path_3, language=None)
        
        # Apply the same text post-processing as in preprocess_ref_audio_text
        if not transcribed_text_fixed.endswith(". ") and not transcribed_text_fixed.endswith("。"):
            if transcribed_text_fixed.endswith("."):
                transcribed_text_fixed += " "
            else:
                transcribed_text_fixed += ". "
        
        print(f"\nTRANSCRIBE FIXED RESULT:")
        print(f"Transcribed text: '{transcribed_text_fixed}'")
        print(f"Text length: {len(transcribed_text_fixed)}")
        print(f"Ends with '. ': {transcribed_text_fixed.endswith('. ')}")
        print(f"Ends with '。': {transcribed_text_fixed.endswith('。')}")
    except Exception as e:
        print(f"Error in fixed transcribe processing: {e}")
        return
    
    # Compare results
    print("\n\n4. COMPARISON:")
    print("-" * 20)
    print(f"Synthesize == Transcribe Original: {processed_ref_text == transcribed_text_original}")
    print(f"Synthesize == Transcribe Fixed:    {processed_ref_text == transcribed_text_fixed}")
    print(f"Original == Fixed:                 {transcribed_text_original == transcribed_text_fixed}")
    
    if processed_ref_text != transcribed_text_fixed:
        print("\nSTILL DIFFERENT! Let's investigate...")
        print(f"Synthesize: '{repr(processed_ref_text)}'")
        print(f"Fixed:      '{repr(transcribed_text_fixed)}'")

if __name__ == "__main__":
    test_transcription_difference()
