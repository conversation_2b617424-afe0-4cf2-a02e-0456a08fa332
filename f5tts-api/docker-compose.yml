services:
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=user
      - RABBITMQ_DEFAULT_PASS=password

  tts-api:
    build:
      context: .
      dockerfile: Dockerfile.tts
    runtime: nvidia
    environment:
      - INSTANCE_ROLE=api
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=user
      - RABBITMQ_PASS=password
      - NVIDIA_VISIBLE_DEVICES=all
    ports:
      - "8000:8000"
    volumes:
      - ~/.cache/huggingface:/root/.cache/huggingface
      - ~/.cache/pip:/root/.cache/pip
    depends_on:
      - rabbitmq

  tts-worker:
    build:
      context: .
      dockerfile: Dockerfile.tts
    runtime: nvidia
    environment:
      - INSTANCE_ROLE=worker
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=user
      - RABBITMQ_PASS=password
      - NVIDIA_VISIBLE_DEVICES=all
    volumes:
      - ~/.cache/huggingface:/root/.cache/huggingface
      - ~/.cache/pip:/root/.cache/pip
    depends_on:
      - rabbitmq

