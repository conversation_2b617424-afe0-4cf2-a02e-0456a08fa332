# Base image with PyTorch, CUDA, and cuDNN
FROM pytorch/pytorch:2.1.0-cuda12.1-cudnn8-runtime

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    libsndfile1 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install uv (dependency manager)
RUN pip install uv

# Copy only files needed to install dependencies
COPY pyproject.toml ./

# Sync dependencies using uv
RUN uv sync

# Now copy the rest of the application
COPY main.py ./
COPY src/ ./src/

# Expose app port
EXPOSE 8000

# Set CUDA environment variable
ENV CUDA_VISIBLE_DEVICES=0

# Default command to run the FastAPI app
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
